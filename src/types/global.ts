import { Socket } from 'socket.io-client';
import { RmiOutlet, TUserLang } from '@types';

export interface DevTools {
  fakeGps: boolean;
  pickLocationMode: boolean;
  location: [number, number];
  realLocation: [number, number];
  isEnableGPS: boolean;
  targetLocation: [number, number];
}

export interface SocketStore {
  socket?: Socket;
  authenticated?: boolean;
}

export interface DiscountPrice {
  beacon: boolean;
  percent: number;
  price: number;
  is_free: boolean;
  expire_at: string;
  base_price: number;
}

export type CoinStatusType = 'found' | 'ongoing' | 'verifying' | 'forfeited';

export type CoinType = 'silver' | 'golden' | 'sentosa-golden-coin' | 'geneo-coin';

export interface BaseLocation {
  lat: number;
  lng: number;
}

export interface AppLocalization {
  lang: TUserLang;
  data: Record<string, string>;
}

export interface FAQItem {
  question: string;
  answer: string;
}

export interface FAQSection {
  header: string;
  list: FAQItem[];
}

export interface UseFAQOptions {
  sections: ComputedRef<FAQSection[]>;
}

export interface UseFAQReturn {
  search: Ref<string>;
  selectedCategory: Ref<string>;
  categoryOptions: ComputedRef<string[]>;
  filteredSections: ComputedRef<FAQSection[]>;
  clearFilters: () => void;
}

export interface UserTacContent {
  content: string;
  subs?: string[];
  style?: string;
}

export interface UserTacData {
  header: string;
  title?: string;
  contents: UserTacContent[];
  styleContent?: string;
}

export type CountryIsoCode = 'SG' | 'MY' | 'ID' | 'TH' | 'VN' | 'JP';
export interface CountryRegion {
  name: string;
  code: string;
  iso: CountryIsoCode;
  flag: string;
  mask: string[];
  currency: string;
  currencyName: string;
  url: string;
}

export interface SurveyAnswer {
  value: string | number;
  size?: string;
  total?: number;
  selected?: boolean;
  title?: string;
  type?: 'area' | 'select' | 'image';
  image?: string;
  active?: boolean;
}

export interface SurveyQuestion {
  order: number;
  id: string;
  q: string;
  sub_q: string;
  a: SurveyAnswer[];
  type: 'select' | 'rate' | 'area' | 'slide';
  min_rate_text?: string;
  max_rate_text?: string;
  multiple?: boolean;
  slide?: {
    step: number;
    unit: string;
  };
  selected?: any;
  condition?: {
    next: (currentQuestion?: SurveyQuestion, allQuestions?: SurveyQuestion[]) => string;
  };
}

export interface UnifyInstructor {
  bubbleText?: string;
  agent?: string;
  tag?: string;
  sequences: Sequences[];
  hiddenAnims?: boolean;
  bubbleAction?: boolean;
}

export interface Sequences {
  target?: string;
  message?: string;
  css?: Record<string, string>;
  backdropCss?: Record<string, string> | boolean;
  delay?: number;
  persistent?: boolean;
  hideClose?: boolean;
  actions?: {
    name?: string;
    cb?: (next: () => void, close: () => void) => void;
    closeX?: (next: () => void, close: () => void) => void;
  };
  images?: string[];
  rmiOutlet?: RmiOutlet | undefined;
}

export type ActionCallback = (
  next: (index?: number) => void,
  close: () => Promise<void>,
) => void | Promise<void>;

export interface SystemMessage {
  id: string;
  text: string;
  type: 'sqkii' | 'timii' | 'nancii' | 'shinobii' | 'rmi';
  agent?: string | undefined;
  tag?: string | undefined;
  hiddenAnims?: boolean;
  messageType: 'sequence' | 'conditional' | 'rmi_conditional';
  persistent?: boolean;
  backdropCss?: Record<string, string> | true;
  images?: string[];
  rmiOutlet?: RmiOutlet;
  condition?: () => boolean;
  onTrigger?: () => void | Promise<void>;
  onClick?: () => void | Promise<void>;
}

<script setup lang="ts">
import { PixiAnims } from '@components';
import { useGlobalInstructor, usePageTracker } from '@composables';
import { useUserStore } from '@stores';
import { BrandSponsors } from '@enums';
import gsap, { Power3, Linear } from 'gsap';
import type { UnifyInstructor, ActionCallback } from '@types';
import TextPlugin from 'gsap/TextPlugin';
import CSSRulePlugin from 'gsap/CSSRulePlugin';

gsap.registerPlugin(TextPlugin, CSSRulePlugin);

interface Emits {
  (e: 'close'): void;
}

const props = withDefaults(defineProps<UnifyInstructor>(), {
  bubbleText: '',
  agent: 'silver-coin',
  hiddenAnims: false,
  bubbleAction: true,
});

const emits = defineEmits<Emits>();

const storeUser = useUserStore();

const { user } = storeToRefs(storeUser);
const { closeUnifyInstructor: closeUnifyInstructorGlobal } = useGlobalInstructor();
const { t } = useI18n();
const { openDialog } = useMicroRoute();
const { tracker } = usePageTracker();

const tl = gsap.timeline();

const currentSequenceIndex = ref(0);
const isActionInProgress = ref(false);

const agentCharacter = computed(() => {
  return `/sov/character/${BrandSponsors.Sqkii}_timii`;
});

const currentSequence = computed(() => props.sequences[currentSequenceIndex.value]);
const hasMessage = computed(() => Boolean(currentSequence.value?.message));
const isLastSequence = computed(() => currentSequenceIndex.value >= props.sequences.length - 1);
const shouldShowSignupOverlay = computed(() => !user.value?.mobile_number && !props.bubbleText);

const backdropStyle = computed(() => {
  const css = currentSequence.value?.backdropCss;
  return typeof css === 'string' ? css : undefined;
});

const instructorStyle = computed(() => {
  return currentSequence.value?.css || {};
});

// Validate teleport target exists
const teleportTarget = computed(() => {
  const target = currentSequence.value?.target || 'body';
  // Ensure target element exists in DOM
  if (typeof target === 'string') {
    const element = document.querySelector(target);
    return element ? target : 'body';
  }
  return 'body';
});

const SELECTORS = {
  TEXT: '#text',
  INSTRUCTOR: '.instructor',
  BACKDROP: '.i_backdrop',
  AGENT: '.agent',
  BODY: '.body',
  BUBBLE_NAME: '.bubble-name',
  TIMII_WAVING: '.timii-waving',
  ACTION: '.action',
} as const;

async function handleAction(action?: ActionCallback): Promise<void> {
  if (isActionInProgress.value || !currentSequence.value) return;

  isActionInProgress.value = true;

  try {
    if (currentSequence.value.persistent) return;

    if (!action) {
      await goToNext();
      return;
    }

    await action(() => void goToNext(), closeUnifyInstructor);
  } finally {
    isActionInProgress.value = false;
  }
}

async function goToNext(targetIndex?: number): Promise<void> {
  if (targetIndex !== undefined && targetIndex < props.sequences.length) {
    currentSequenceIndex.value = targetIndex;
    return;
  }

  if (!isLastSequence.value) {
    currentSequenceIndex.value += 1;
    return;
  }

  await closeUnifyInstructor();
}

async function closeUnifyInstructor(): Promise<void> {
  await hideWithTransition();
  closeUnifyInstructorGlobal();

  tracker({
    id: 'sqkii_instructor',
    action: 'click',
    data: {
      target: 'tap',
      message: currentSequence.value?.message || '',
    },
  });
  emits('close');

  isActionInProgress.value = false;
}

async function animateText(): Promise<void> {
  if (!hasMessage.value || !currentSequence.value?.message) return;

  const textElement = SELECTORS.TEXT;

  gsap.set(textElement, { pointerEvents: 'none' });

  try {
    await gsap.fromTo(
      textElement,
      { text: '' },
      {
        delay: currentSequenceIndex.value ? 0 : 1,
        text: currentSequence.value.message,
        duration: 1,
        ease: Linear.easeNone,
      },
    );
  } finally {
    gsap.set(textElement, { pointerEvents: 'all' });
  }
}

watchEffect(() => void animateText(), { flush: 'post' });

async function showWithTransition(): Promise<void> {
  const instructorElement = SELECTORS.INSTRUCTOR;

  // Check if elements exist before animating
  const elements = [SELECTORS.AGENT, SELECTORS.BODY, SELECTORS.BUBBLE_NAME, SELECTORS.TIMII_WAVING];
  const existingElements = elements.filter((selector) => document.querySelector(selector));

  if (existingElements.length === 0) return;

  gsap.set(instructorElement, { pointerEvents: 'none' });

  try {
    if (!hasMessage.value) return;

    await tl
      .fromTo(SELECTORS.AGENT, { y: 10, opacity: 0 }, { y: '-50%', opacity: 1, duration: 0.5 })
      .fromTo(
        SELECTORS.BODY,
        { opacity: 0, width: '0%' },
        { opacity: 1, width: '100%', duration: 1 },
      )
      .fromTo(
        SELECTORS.BUBBLE_NAME,
        { opacity: 0, x: -5 },
        { opacity: 1, x: 0, duration: 0.5 },
        '-=0.25',
      )
      .fromTo(
        SELECTORS.TIMII_WAVING,
        { opacity: 0, y: 10 },
        { opacity: 1, y: 0, duration: 0.5 },
        '-=1.25',
      );
  } finally {
    gsap.set(instructorElement, { pointerEvents: 'all' });
  }
}

async function hideWithTransition(): Promise<void> {
  const elements = [SELECTORS.INSTRUCTOR, SELECTORS.BACKDROP];

  gsap.set(elements, { pointerEvents: 'none' });

  try {
    if (!hasMessage.value) return;

    await tl
      .to(SELECTORS.BODY, {
        y: -5,
        opacity: 0,
        duration: 0.5,
        ease: Power3.easeIn,
      })
      .to(
        [SELECTORS.AGENT, SELECTORS.BUBBLE_NAME, SELECTORS.TIMII_WAVING],
        { opacity: 0, y: 10 },
        '-=0.5',
      );
  } finally {
    gsap.set(elements, { pointerEvents: 'all' });
  }
}

function handleSignUp(): void {
  if (!props.bubbleAction) return;
  openDialog('signup');
}

onMounted(async () => {
  await nextTick();
  // Ensure DOM is ready before starting transitions
  if (document.body && hasMessage.value) {
    await showWithTransition();
  }
});

onBeforeUnmount(() => {
  // Kill all animations immediately to prevent DOM manipulation errors
  tl.kill();

  const elementsToCleanup = [
    SELECTORS.ACTION,
    SELECTORS.TEXT,
    SELECTORS.INSTRUCTOR,
    SELECTORS.BACKDROP,
    SELECTORS.AGENT,
    SELECTORS.BODY,
    SELECTORS.BUBBLE_NAME,
    SELECTORS.TIMII_WAVING,
  ];

  elementsToCleanup.forEach((selector) => {
    gsap.killTweensOf(selector);
  });
});
</script>

<template>
  <Teleport :to="teleportTarget">
    <!-- Animated backdrop -->
    <Transition
      appear
      enter-active-class="animated fadeIn"
      leave-active-class="animated fadeOut"
      :duration="300"
    >
      <div
        v-if="currentSequence?.backdropCss"
        class="i_backdrop"
        v-bind="backdropStyle ? { style: backdropStyle } : {}"
        @click="handleAction(currentSequence.actions?.cb)"
      />
    </Transition>

    <!-- Main instructor container -->
    <div class="instructor" :style="instructorStyle" ref="instructor">
      <div v-if="hasMessage" class="wrapper" @click="handleAction(currentSequence?.actions?.cb)">
        <!-- Agent avatar -->
        <div class="agent flex justify-center items-center">
          <Icon :name="agent" :size="35" />
          <!-- Animated Timii for season starting -->
          <PixiAnims
            v-show="!hiddenAnims"
            :width="100"
            :height="100"
            name="timii-waving"
            json="timii-waving.json"
            size="contain"
            :animation-speed="0.25"
            class="timii-waving absolute top-[-97px] left-1/2 -translate-x-1/2 -z-10"
          />

          <!-- Static Timii icon -->
          <Icon
            v-show="hiddenAnims"
            :name="agentCharacter"
            :size="55"
            class="timii-waving absolute top-[-50px] left-1/2 -translate-x-1/2 -z-10"
          />
        </div>

        <!-- Message body -->
        <div class="body">
          <!-- Bubble name/header -->
          <div class="bubble-name">
            <template v-if="bubbleText">
              <div class="bubble-name-text" v-html="bubbleText" />
            </template>
            <template v-else>
              <!-- Signup overlay for non-registered users -->
              <div
                v-if="shouldShowSignupOverlay"
                class="absolute w-full -top-3 -bottom-3 left-0 z-50"
                @click="handleSignUp"
              />
              <div
                class="bubble-name-text"
                v-html="
                  t('TIPSANDTRICKS_TIMIIMESSAGE_GREETING', {
                    LABEL: user?.mobile_number
                      ? t('SIGNUP_CREATED_HUNTER')
                      : t('SIGNUP_CREATED_GUEST'),
                    USER_ID: user?.hunter_id,
                  })
                "
              />
            </template>
          </div>

          <!-- Message text -->
          <div id="text" class="text-sm" v-html="currentSequence?.message" />
        </div>
      </div>
    </div>
  </Teleport>
</template>

<style lang="scss" scoped>
.i_backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 7000;
}
.instructor {
  z-index: 10001;
  position: fixed;
  left: 30px;
  bottom: 40px;
  right: 80px;
  transition: bottom 0.4s ease-in-out;

  .wrapper {
    position: relative;
    display: flex;
    width: 100%;
    min-height: 90px;
  }
  .agent {
    position: absolute;
    left: -12px;
    top: 50%;
    width: 54px;
    height: 54px;
    background-image: url('/imgs/coin-bg.png');
    background-size: 100% 100%;
    z-index: 99;
  }
  .body {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 10px 10px 40px;
    background-size: 100% 100%;
    background-image: url('/imgs/intro-square-bg.png');
    background-repeat: no-repeat;
    #text {
      margin-bottom: 10px;
      margin-left: 10px;
      width: 100vw;
      padding: 10px 0 15px;
    }
    .bubble-name {
      position: absolute;
      left: 45px;
      top: -10px;
      height: 18px;
      padding: 0 8px;
      background: linear-gradient(270deg, #6843d9 2.37%, #4d3ea5 100%);
      border-radius: 2px;
      transform: skewX(-10deg);
      &-text {
        position: relative;
        transform: skewX(10deg) translateY(-50%);
        font-size: 12px;
        line-height: 14px;
        margin-top: 11px;
        width: 100%;
        height: 100%;
        white-space: nowrap;
      }
    }
  }
}
</style>
